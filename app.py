"""
HF Spaces专用启动文件
针对Hugging Face Spaces优化的双语字幕工具
"""

import os
import sys

# 设置环境变量
os.environ["GRADIO_SERVER_NAME"] = "0.0.0.0"
os.environ["GRADIO_SERVER_PORT"] = "7860"

# 导入主应用
from main import BilingualSubtitleApp

def create_hf_spaces_app():
    """为HF Spaces创建优化的应用"""
    app = BilingualSubtitleApp()
    interface = app.create_interface()
    
    # HF Spaces特定配置
    return interface

if __name__ == "__main__":
    # 创建应用
    demo = create_hf_spaces_app()
    
    # 为HF Spaces启动
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,  # HF Spaces不需要share
        show_error=True,
        enable_queue=True  # 启用队列以处理并发
    )