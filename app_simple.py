"""
HF Spaces专用简化启动文件
解决Gradio 4.44.1版本的JSON schema bug
"""

import os
import sys
import gradio as gr

# 设置环境变量
os.environ["GRADIO_SERVER_NAME"] = "0.0.0.0"
os.environ["GRADIO_SERVER_PORT"] = "7860"

def create_simple_interface():
    """创建简化的界面，避免Gradio bug"""
    
    with gr.Blocks(
        title="双语字幕工具",
        theme=gr.themes.Soft()
    ) as interface:
        
        gr.Markdown("""
        # 🎬 双语字幕工具
        
        一个轻量级的工具，为带英文音频的视频添加中英双语字幕。
        
        **注意**: 当前版本正在修复HF Spaces兼容性问题。
        """)
        
        with gr.Row():
            with gr.Column():
                # 简化的文件上传
                video_input = gr.File(
                    label="📁 上传视频文件"
                )
                
                # 简化的配置
                whisper_model = gr.Dropdown(
                    choices=["tiny", "base", "small"],
                    value="base",
                    label="🎤 Whisper模型大小"
                )
                
                baidu_appid = gr.Textbox(
                    label="🔑 百度翻译APPID",
                    placeholder="请输入百度翻译API的APPID"
                )
                
                baidu_appkey = gr.Textbox(
                    label="🔐 百度翻译APPKEY",
                    placeholder="请输入百度翻译API的密钥"
                )
                
                process_btn = gr.Button(
                    "🚀 开始处理",
                    variant="primary"
                )
            
            with gr.Column():
                status_display = gr.Textbox(
                    label="📊 处理状态",
                    value="等待上传视频文件...",
                    interactive=False
                )
                
                output_files = gr.File(
                    label="📁 输出文件",
                    file_count="multiple"
                )
        
        def simple_process(video, model, appid, appkey):
            """简化的处理函数"""
            if video is None:
                return "❌ 请上传视频文件", None
            
            return "⚠️ 功能正在修复中，请稍后再试", None
        
        process_btn.click(
            fn=simple_process,
            inputs=[video_input, whisper_model, baidu_appid, baidu_appkey],
            outputs=[status_display, output_files]
        )
    
    return interface

if __name__ == "__main__":
    # 创建简化应用
    demo = create_simple_interface()
    
    # 启动
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )
