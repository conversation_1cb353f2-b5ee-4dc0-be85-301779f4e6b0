[project]
name = "bilingual-subtitle-tool"
version = "0.1.0"
description = "A lightweight tool for adding bilingual subtitles to videos with English audio"
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "gradio>=4.0.0",
    "moviepy>=1.0.3",
    "faster-whisper>=0.10.0",
    "pysrt>=1.1.2",
    "requests>=2.28.0",
    "torch>=2.0.0",
    "torchaudio>=2.0.0",
    "ffmpeg-python>=0.2.0",
    "numpy>=1.21.0",
    "python-dotenv>=1.0.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 88
target-version = ['py38']

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
